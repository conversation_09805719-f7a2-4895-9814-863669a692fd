{"name": "clash-strategic-webapp", "devDependencies": {"@babel/preset-env": "^7.27.2", "@eslint/js": "^9.21.0", "@playwright/test": "^1.53.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.2", "@types/node": "^24.0.4", "babel-jest": "^30.0.2", "eslint": "^9.21.0", "eslint-config-jquery": "^3.0.2", "eslint-plugin-jquery": "^1.5.1", "globals": "^16.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jquery": "^3.7.1", "semantic-release": "^24.2.4"}, "scripts": {"test": "jest"}, "version": "1.2.1"}